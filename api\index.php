<?php
/**
 * API الرئيسي لنظام إدارة المراكز الصحية
 * Main API for Healthcare Centers Management System
 */

// إعدادات CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Content-Type: application/json; charset=utf-8');

// التعامل مع طلبات OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// تضمين ملفات الإعداد
require_once '../config/database.php';
require_once 'classes/ApiResponse.php';
require_once 'classes/AuthManager.php';
require_once 'classes/UserManager.php';
require_once 'classes/ChildrenManager.php';
require_once 'classes/VaccineManager.php';
require_once 'classes/MedicineManager.php';
require_once 'classes/ContraceptiveManager.php';
require_once 'classes/MessageManager.php';
require_once 'classes/TaskManager.php';
require_once 'classes/NotificationManager.php';
require_once 'classes/StatsManager.php';

// بدء الجلسة
session_start();

try {
    // إنشاء اتصال قاعدة البيانات
    $database = new Database();
    $db = $database->getConnection();

    // فحص وجود الجداول وإنشاؤها إذا لم تكن موجودة
    if (!$database->checkTablesExist()) {
        $database->createDatabase();
    }

    // الحصول على المسار والطريقة
    $request_uri = $_SERVER['REQUEST_URI'];
    $path = parse_url($request_uri, PHP_URL_PATH);
    $path = str_replace('/api', '', $path);
    $method = $_SERVER['REQUEST_METHOD'];

    // تحليل المسار
    $path_parts = explode('/', trim($path, '/'));
    $endpoint = $path_parts[0] ?? '';
    $action = $path_parts[1] ?? '';
    $id = $path_parts[2] ?? '';

    // الحصول على البيانات المرسلة
    $input = json_decode(file_get_contents('php://input'), true);
    if (json_last_error() !== JSON_ERROR_NONE && !empty(file_get_contents('php://input'))) {
        throw new Exception('بيانات JSON غير صحيحة');
    }

    // إنشاء مدير المصادقة
    $authManager = new AuthManager($db);

    // المسارات التي لا تحتاج مصادقة
    $public_endpoints = ['auth', 'install'];

    // التحقق من المصادقة للمسارات المحمية
    if (!in_array($endpoint, $public_endpoints)) {
        $user = $authManager->getCurrentUser();
        if (!$user) {
            ApiResponse::error('غير مصرح بالوصول', 401);
            exit();
        }
    }

    // توجيه الطلبات حسب النقطة النهائية
    switch ($endpoint) {
        case 'auth':
            handleAuthRequests($authManager, $action, $method, $input);
            break;

        case 'users':
            $userManager = new UserManager($db);
            handleUserRequests($userManager, $action, $method, $input, $id);
            break;

        case 'children':
            $childrenManager = new ChildrenManager($db);
            handleChildrenRequests($childrenManager, $action, $method, $input, $id);
            break;

        case 'vaccines':
            $vaccineManager = new VaccineManager($db);
            handleVaccineRequests($vaccineManager, $action, $method, $input, $id);
            break;

        case 'medicines':
            $medicineManager = new MedicineManager($db);
            handleMedicineRequests($medicineManager, $action, $method, $input, $id);
            break;

        case 'contraceptives':
            $contraceptiveManager = new ContraceptiveManager($db);
            handleContraceptiveRequests($contraceptiveManager, $action, $method, $input, $id);
            break;

        case 'messages':
            $messageManager = new MessageManager($db);
            handleMessageRequests($messageManager, $action, $method, $input, $id);
            break;

        case 'tasks':
            $taskManager = new TaskManager($db);
            handleTaskRequests($taskManager, $action, $method, $input, $id);
            break;

        case 'notifications':
            $notificationManager = new NotificationManager($db);
            handleNotificationRequests($notificationManager, $action, $method, $input, $id);
            break;

        case 'stats':
            $statsManager = new StatsManager($db);
            handleStatsRequests($statsManager, $action, $method, $input, $id);
            break;

        case 'install':
            handleInstallRequests($database, $action, $method);
            break;

        default:
            ApiResponse::error('نقطة نهاية غير موجودة', 404);
            break;
    }

} catch (Exception $e) {
    error_log('API Error: ' . $e->getMessage());
    ApiResponse::error($e->getMessage(), 500);
}

/**
 * التعامل مع طلبات المصادقة
 */
function handleAuthRequests($authManager, $action, $method, $input) {
    switch ($action) {
        case 'login':
            if ($method === 'POST') {
                $username = $input['username'] ?? '';
                $password = $input['password'] ?? '';
                
                $result = $authManager->login($username, $password);
                if ($result) {
                    ApiResponse::success($result, 'تم تسجيل الدخول بنجاح');
                } else {
                    ApiResponse::error('اسم المستخدم أو كلمة المرور غير صحيحة', 401);
                }
            } else {
                ApiResponse::error('طريقة غير مدعومة', 405);
            }
            break;

        case 'logout':
            if ($method === 'POST') {
                $authManager->logout();
                ApiResponse::success(null, 'تم تسجيل الخروج بنجاح');
            } else {
                ApiResponse::error('طريقة غير مدعومة', 405);
            }
            break;

        case 'check':
            if ($method === 'GET') {
                $user = $authManager->getCurrentUser();
                if ($user) {
                    ApiResponse::success($user, 'المستخدم مسجل الدخول');
                } else {
                    ApiResponse::error('المستخدم غير مسجل الدخول', 401);
                }
            } else {
                ApiResponse::error('طريقة غير مدعومة', 405);
            }
            break;

        default:
            ApiResponse::error('إجراء غير موجود', 404);
            break;
    }
}

/**
 * التعامل مع طلبات المستخدمين
 */
function handleUserRequests($userManager, $action, $method, $input, $id) {
    switch ($action) {
        case 'list':
            if ($method === 'GET') {
                $users = $userManager->getAllUsers();
                ApiResponse::success($users);
            } else {
                ApiResponse::error('طريقة غير مدعومة', 405);
            }
            break;

        case 'create':
            if ($method === 'POST') {
                $result = $userManager->createUser($input);
                if ($result) {
                    ApiResponse::success($result, 'تم إنشاء المستخدم بنجاح');
                } else {
                    ApiResponse::error('فشل في إنشاء المستخدم');
                }
            } else {
                ApiResponse::error('طريقة غير مدعومة', 405);
            }
            break;

        case 'update':
            if ($method === 'PUT' && $id) {
                $result = $userManager->updateUser($id, $input);
                if ($result) {
                    ApiResponse::success($result, 'تم تحديث المستخدم بنجاح');
                } else {
                    ApiResponse::error('فشل في تحديث المستخدم');
                }
            } else {
                ApiResponse::error('طريقة غير مدعومة أو معرف مفقود', 405);
            }
            break;

        case 'delete':
            if ($method === 'DELETE' && $id) {
                $result = $userManager->deleteUser($id);
                if ($result) {
                    ApiResponse::success(null, 'تم حذف المستخدم بنجاح');
                } else {
                    ApiResponse::error('فشل في حذف المستخدم');
                }
            } else {
                ApiResponse::error('طريقة غير مدعومة أو معرف مفقود', 405);
            }
            break;

        default:
            ApiResponse::error('إجراء غير موجود', 404);
            break;
    }
}

/**
 * التعامل مع طلبات الأطفال
 */
function handleChildrenRequests($childrenManager, $action, $method, $input, $id) {
    switch ($action) {
        case 'list':
            if ($method === 'GET') {
                $page = $_GET['page'] ?? 1;
                $limit = $_GET['limit'] ?? 20;
                $search = $_GET['search'] ?? '';
                
                $result = $childrenManager->getChildren($page, $limit, $search);
                ApiResponse::success($result);
            } else {
                ApiResponse::error('طريقة غير مدعومة', 405);
            }
            break;

        case 'create':
            if ($method === 'POST') {
                $result = $childrenManager->createChild($input);
                if ($result) {
                    ApiResponse::success($result, 'تم إضافة الطفل بنجاح');
                } else {
                    ApiResponse::error('فشل في إضافة الطفل');
                }
            } else {
                ApiResponse::error('طريقة غير مدعومة', 405);
            }
            break;

        case 'get':
            if ($method === 'GET' && $id) {
                $child = $childrenManager->getChild($id);
                if ($child) {
                    ApiResponse::success($child);
                } else {
                    ApiResponse::error('الطفل غير موجود', 404);
                }
            } else {
                ApiResponse::error('طريقة غير مدعومة أو معرف مفقود', 405);
            }
            break;

        case 'update':
            if ($method === 'PUT' && $id) {
                $result = $childrenManager->updateChild($id, $input);
                if ($result) {
                    ApiResponse::success($result, 'تم تحديث بيانات الطفل بنجاح');
                } else {
                    ApiResponse::error('فشل في تحديث بيانات الطفل');
                }
            } else {
                ApiResponse::error('طريقة غير مدعومة أو معرف مفقود', 405);
            }
            break;

        case 'delete':
            if ($method === 'DELETE' && $id) {
                $result = $childrenManager->deleteChild($id);
                if ($result) {
                    ApiResponse::success(null, 'تم حذف الطفل بنجاح');
                } else {
                    ApiResponse::error('فشل في حذف الطفل');
                }
            } else {
                ApiResponse::error('طريقة غير مدعومة أو معرف مفقود', 405);
            }
            break;

        default:
            ApiResponse::error('إجراء غير موجود', 404);
            break;
    }
}

/**
 * التعامل مع طلبات التثبيت
 */
function handleInstallRequests($database, $action, $method) {
    switch ($action) {
        case 'check':
            if ($method === 'GET') {
                $exists = $database->checkTablesExist();
                ApiResponse::success(['installed' => $exists]);
            } else {
                ApiResponse::error('طريقة غير مدعومة', 405);
            }
            break;

        case 'install':
            if ($method === 'POST') {
                try {
                    $database->createDatabase();
                    ApiResponse::success(null, 'تم تثبيت قاعدة البيانات بنجاح');
                } catch (Exception $e) {
                    ApiResponse::error('فشل في تثبيت قاعدة البيانات: ' . $e->getMessage());
                }
            } else {
                ApiResponse::error('طريقة غير مدعومة', 405);
            }
            break;

        default:
            ApiResponse::error('إجراء غير موجود', 404);
            break;
    }
}

// إضافة المزيد من دوال التعامل مع الطلبات...
// (سيتم إضافتها في الملفات التالية)
?>
